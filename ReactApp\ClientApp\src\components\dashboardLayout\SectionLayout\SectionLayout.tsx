import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ody, Breadcrumb } from "@progress/kendo-react-layout";
import "./SectionLayout.scss";

interface SectionLayoutProps {
  children: React.ReactNode;
  headerActions?: React.ReactNode;
}

const SectionLayout: React.FC<SectionLayoutProps> = ({ children, headerActions }) => {
  return (
    <div className="published-files-page-container">
      <Card className="published-files-card">
        <CardHeader className="published-files-card__header">
          <Breadcrumb
            data={[
              { text: "Anderson Finance LLC", id: "home" },
              { text: "Published Files", id: "published-files" },
              { text: "All Directories", id: "all-directories" },
            ]}
            onItemSelect={() => {}}
            className="published-files-breadcrumb"
          />
          {headerActions && (
            <div className="published-files-actions">
              {headerActions}
            </div>
          )}
        </CardHeader>
        <CardBody className="published-files-card__body">
          {children}
        </CardBody>
      </Card>
    </div>
  );
};

export default SectionLayout;
