/* grid height inside the body wrapper */
.published-files-card__body .k-grid {
  height: 100%;
}

/* Custom Pager Styling */
.custom-pager-wrap {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-top: 1px solid var(--kendo-color-base-emphasis);
}

/* strip background/borders off the pager container */
.custom-pager-wrap .k-pager {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0;
}

/* push the default “1–10 of 30 items” text to the far right */
.custom-default-info {
  font-size: 0.875rem;
  color: var(--kendo-color-text-secondary);
  margin-left: auto;
}

.custom-refresh-btn {
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.75rem;

  svg {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--kendo-color-text-secondary);
  }
}
