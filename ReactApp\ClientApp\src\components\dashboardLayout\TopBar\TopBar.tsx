import { useRef, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { DropDownList } from "@progress/kendo-react-dropdowns";
import type { DropDownListChangeEvent } from "@progress/kendo-react-dropdowns";
import {
  Avatar,
  AppBar,
  AppBarSection,
  AppBarSpacer,
} from "@progress/kendo-react-layout";
import { SvgIcon } from "@progress/kendo-react-common";
import { Button } from "@progress/kendo-react-buttons";
import { Badge, BadgeContainer } from "@progress/kendo-react-indicators";
import { Popup } from "@progress/kendo-react-popup";
import {
  bellIcon,
  searchIcon,
  gearIcon,
  menuIcon,
  logoutIcon,
} from "@progress/kendo-svg-icons";

import "./TopBar.scss";
import logo from "@/assets/logo.png";
import logger from "@/utils/logger";
import { useAuth } from "@/hooks/useAuth";

const mockUser = {
  fullName: "<PERSON>",
};

const mockCompanies = [
  { text: "Anderson Finance LLC", id: 1 },
  { text: "Vision Capital", id: 2 },
  { text: "Nexon Partners", id: 3 },
];

export const TopBar = () => {
  const { t } = useTranslation("dashboard");
  const { logout } = useAuth();

  const [selectedCompany, setSelectedCompany] = useState(mockCompanies[0]);
  const [overflowOpen, setOverflowOpen] = useState(false);
  const [avatarMenuOpen, setAvatarMenuOpen] = useState(false);
  const menuAnchor = useRef<HTMLDivElement>(null);
  const avatarAnchor = useRef<HTMLDivElement>(null);

  const userInitials = mockUser.fullName
    .split(" ")
    .map((name) => name[0])
    .join("")
    .toUpperCase();

  const handleCompanyChange = (e: DropDownListChangeEvent) => {
    setSelectedCompany(e.value);
    logger.info("Company changed:", e.value);
  };

  const toggleOverflow = () => setOverflowOpen((prev) => !prev);
  const closeOverflow = () => setOverflowOpen(false);

  const toggleAvatarMenu = () => setAvatarMenuOpen((prev) => !prev);
  const closeAvatarMenu = () => setAvatarMenuOpen(false);

  const handleLogout = () => {
    closeOverflow();
    closeAvatarMenu();
    logout();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuAnchor.current &&
        !menuAnchor.current.contains(event.target as Node) &&
        avatarAnchor.current &&
        !avatarAnchor.current.contains(event.target as Node)
      ) {
        closeOverflow();
        closeAvatarMenu();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <AppBar className="topbar" positionMode="static">
      <AppBarSection className="topbar__left">
        <img src={logo} alt="IRIS Logo" className="topbar__logo-image" />
        <span className="topbar__divider" />
        <span className="topbar__title">{t("portal_title")}</span>
      </AppBarSection>

      <AppBarSpacer />

      <AppBarSection className="topbar__right">
        <DropDownList
          data={mockCompanies}
          textField="text"
          value={selectedCompany}
          onChange={handleCompanyChange}
          className="topbar__company-dropdown"
        />

        <SvgIcon icon={searchIcon} className="topbar__icon" />

        <BadgeContainer>
          <Button
            fillMode="flat"
            type="button"
            svgIcon={bellIcon}
            className="topbar__bell-button"
          >
            <Badge rounded="small" themeColor="error" size="small">
              12
            </Badge>
          </Button>
        </BadgeContainer>

        <SvgIcon icon={gearIcon} className="topbar__icon" />

        <div ref={menuAnchor} className="topbar__overflow-button">
          <Button
            fillMode="flat"
            type="button"
            svgIcon={menuIcon}
            onClick={toggleOverflow}
            aria-haspopup="true"
            aria-expanded={overflowOpen}
            aria-controls="user-menu-popup"
          />
        </div>

        {overflowOpen && (
          <Popup
            anchor={menuAnchor.current}
            show={overflowOpen}
            popupClass="topbar__popup"
            anchorAlign={{ horizontal: "right", vertical: "bottom" }}
            popupAlign={{ horizontal: "right", vertical: "top" }}
          >
            <div
              id="user-menu-popup"
              className="topbar__popup-menu"
              role="menu"
            >
              <ul>
                <li role="menuitem" tabIndex={0} onClick={closeOverflow}>
                  Notifications
                </li>
                <li role="menuitem" tabIndex={0} onClick={closeOverflow}>
                  Settings
                </li>
                <li
                  role="menuitem"
                  tabIndex={0}
                  onClick={handleLogout}
                  onKeyDown={(e) => e.key === "Enter" && handleLogout()}
                >
                  Logout
                </li>
              </ul>
            </div>
          </Popup>
        )}

        <div className="topbar__avatar-container" ref={avatarAnchor}>
          <div
            className="topbar__avatar-wrapper"
            onClick={toggleAvatarMenu}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === "Enter" && toggleAvatarMenu()}
          >
            <Avatar type="text" size="medium" className="topbar__avatar">
              {userInitials}
            </Avatar>
          </div>

          {avatarMenuOpen && (
            <div className="topbar__avatar-dropdown">
              <Button
                fillMode="flat"
                type="button"
                svgIcon={logoutIcon}
                onClick={handleLogout}
                className="topbar__logout-button"
              >
                Logout
              </Button>
            </div>
          )}
        </div>
      </AppBarSection>
    </AppBar>
  );
};
