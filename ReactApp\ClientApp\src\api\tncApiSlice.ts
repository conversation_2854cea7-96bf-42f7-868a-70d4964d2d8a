

import { baseQueryWithReauth } from './interceptorsSlice';
import type { AcceptTncResponse, EvaluateTncResponse } from '@/types/TermsAndConditions';
import { createApi } from '@reduxjs/toolkit/query/react';
import httpVerbs from '@/utils/http/httpVerbs';


export const tncApiSlice = createApi({
    reducerPath: '/tnc',
    baseQuery: baseQueryWithReauth,
    endpoints: (builder) => ({
        evaluateTnc: builder.query<EvaluateTncResponse, void>({
            query: () => ({
                url: `api/terms-and-conditions/evaluate`,
                headers: {
                    'Content-Type': 'application/json',

                },
                method: httpVerbs.GET,
            }),
        }),
        acceptTnc: builder.mutation<AcceptTncResponse, { termAndConditionId: number, triggerType: string }>({
            query: ({ termAndConditionId, triggerType }) => ({
                url: `api/terms-and-conditions/accept`,
                method: httpVerbs.POST,
                body: { termAndConditionId, triggerType },
            }),
        }),
        fetchTncDocument: builder.query<Blob, string>({
            query: (documentUrl) => ({
                url: `${documentUrl}`,
                responseHandler: (response) => response.blob(),
            }),
        }),
    })
});

export const { useEvaluateTncQuery, useAcceptTncMutation, useFetchTncDocumentQuery } = tncApiSlice;
