import React, { useEffect } from "react";
import { useOktaAuth } from "@okta/okta-react";
import { TncStatus } from "@/types/TermsAndConditions";
import { useNavigate, useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { useEvaluateTncQuery } from "@/api/tncApiSlice";
import { ROUTES } from "@/constants/routes";

interface RequireAuthProps {
  children: React.ReactElement;
}

export default function RequireAuth({ children }: RequireAuthProps) {
  const { authState, oktaAuth } = useOktaAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const isTncValidated = useAppSelector((state) => state.app.tnc.isValidated);

  const {
    data: tncData,
    isLoading: tncLoading,
    error: tncError,
  } = useEvaluateTncQuery(undefined, {
    skip: !authState?.isAuthenticated || isTncValidated,
  });

  useEffect(() => {
    if (authState?.isAuthenticated === false) {
      oktaAuth.signInWithRedirect({ originalUri: window.location.pathname });
    }
  }, [authState, oktaAuth]);

  useEffect(() => {
    if (
      authState?.isAuthenticated &&
      !isTncValidated &&
      !tncLoading &&
      !tncError &&
      tncData
    ) {
      if (tncData.status === TncStatus.PENDING_ACCEPTANCE) {
        if (location.pathname !== ROUTES.TERMS_AND_CONDITIONS) {
          navigate(ROUTES.TERMS_AND_CONDITIONS, {
            state: { from: location.pathname },
          });
        }
      }
    }
  }, [
    authState?.isAuthenticated,
    tncData,
    tncLoading,
    tncError,
    dispatch,
    navigate,
    location.pathname,
    isTncValidated,
  ]);

  if (!authState || authState.isPending || tncLoading) {
    return <div>Loading...</div>;
  }

  return authState.isAuthenticated ? children : null;
}
