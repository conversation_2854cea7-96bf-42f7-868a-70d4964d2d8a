import { Card } from "@progress/kendo-react-layout";
import { useTranslation } from "react-i18next";

interface Props {
  name: string;
  description: string;
}

export default function SamplePreview({ name, description }: Props) {
  const { t } = useTranslation("dashboard");

  return (
    <Card className="sample-preview">
      <h3>{t("preview.title") || "Preview"}</h3>
      <p>
        <strong>{t("preview.name") || "Name"}:</strong> {name}
      </p>
      <p>
        <strong>{t("preview.description") || "Description"}:</strong>{" "}
        {description}
      </p>
    </Card>
  );
}
