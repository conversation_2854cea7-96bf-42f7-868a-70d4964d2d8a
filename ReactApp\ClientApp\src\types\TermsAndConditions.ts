export enum TriggerType {
    FirstLogin = 'first_login',
    Updated = 'updated',
    Monthly = 'monthly',
    Annually = 'annually',
    Inactivity = 'inactivity',
};

export interface TncFile {
    fileName: string;
    documentUrl: string;
}

export interface EvaluateTncResponse {
    status: string;
    termAndConditionId?: number;
    statementOfAgreement?: string;
    file?: TncFile;
    triggerType?: TriggerType;
};

export const TncTags = {
    EvaluateTnc: 'EvaluateTnc'
};

export interface AcceptTncResponse {
    message: string;
};

export interface TncDocument extends TncFile {
    termAndConditionId: number;
    statementOfAgreement: string;
    triggerType: string;
};

export interface TncDocumentState {
    isValidated: boolean;
    document: TncDocument | null;
}

export const TncStatus = {
    ACCEPTED: 'Accepted',
    PENDING_ACCEPTANCE: "PendingAcceptance"
}