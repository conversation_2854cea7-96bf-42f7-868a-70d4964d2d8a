:root {
  --app-header-height: 65px;
  --page-bottom-offset: 50px;
}

.published-files-page-container {
  height: calc(100vh - var(--app-header-height) - var(--page-bottom-offset));
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.published-files-card {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--kendo-color-base-emphasis);
  border-radius: 4px;
  overflow: hidden;

  &__header {
    padding: 1rem;
    background: var(--kendo-toolbar-bg);
    border-bottom: 1px solid var(--kendo-color-base-emphasis);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .published-files-breadcrumb {
      flex: 1;
    }

    .published-files-actions {
      display: flex;
      gap: 0.75rem;
      align-items: center;

      .published-files-searchbox {
        width: 200px;
      }
    }
  }

  &__body {
    flex: 1 1 auto;
    min-height: 0;
    overflow: hidden;
  }
}
