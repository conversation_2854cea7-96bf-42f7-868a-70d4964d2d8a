import React from 'react';
import { Card } from '@progress/kendo-react-layout';
import { Fa<PERSON>ser, FaLock, FaCog, FaChevronRight } from 'react-icons/fa';
import'./portalControlsPage.scss';
import logger from '@/utils/logger';

interface PortalControlItem {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
}
export const PortalControlsPage: React.FC = () => {
  const controlItems: PortalControlItem[] = [
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Manage portal users and assign portal roles',
      icon: FaUser,
      onClick: () => {
        // TODO: Navigate to user management
        logger.info('Navigate to User Management');
      }
    },
    {
      id: 'security-groups',
      title: 'Security Groups',
      description: 'Create and manage permission-based user groups for file access',
      icon: FaLock,
      onClick: () => {
        // TODO: Navigate to security groups
        logger.info('Navigate to Security Groups');
      }
    },
    {
      id: 'permissions',
      title: 'Permissions',
      description: 'Set security options for security groups',
      icon: FaCog,
      onClick: () => {
        // TODO: Navigate to permissions
        logger.info('Navigate to Permissions');
      }
    }
  ];
  const renderControlCard = (item: PortalControlItem) => {
    const IconComponent = item.icon;
    return (
      <Card
        key={item.id}
        className="portalControlCard"
        onClick={item.onClick}
      >
        <div className="cardContent">
          <IconComponent className="cardIcon" />
          <div className="textSection">
            <h3 className="cardTitle">{item.title}</h3>
            <p className="cardDescription">{item.description}</p>
          </div>
          <FaChevronRight className="arrowIcon" />
        </div>
      </Card>
    );
  };
  return (
    <div className="portalControlsContainer">
      {controlItems.map(renderControlCard)}
    </div>
  );
};
export default PortalControlsPage;