// ReactApp\ClientApp\src\features\submitedFiles\hooks\useSubmitedFilesController.ts

import { useState, useMemo } from "react";
import { orderBy, filterBy } from "@progress/kendo-data-query";
import type {
  SortDescriptor,
  CompositeFilterDescriptor,
} from "@progress/kendo-data-query";
import type {
  GridSelectionChangeEvent,
  GridHeaderSelectionChangeEvent,
  GridPageChangeEvent,
} from "@progress/kendo-react-grid";
import type { SelectDescriptor } from "@progress/kendo-react-data-tools";
import logger from "@/utils/logger";

export interface FileRecord {
  id: number;
  fileName: string;
  fileType: string;
  fileSize: string;
  fileStatus: string;
  uploadedDate: Date;
  approvedDate: Date;
  approvedUser: string;
  rejectedDate: Date;
  rejectedUser: string;
}

// Generate mock data for submitted files
const generateInitialData = (): FileRecord[] => {
  const filePrefixes = ["Report_", "Tax_File_", "Income_Tax_"];
  const fileTypes = ["PDF", "CSV", "JPEG"];
  const fileStatuses = ["Pending", "Accepted", "Rejected"];
  const userNames = ["Alice", "Bob", "Carol", "Dave", "Eve"];

  return Array.from({ length: 30 }).map((_, i) => {
    const prefix = filePrefixes[Math.floor(Math.random() * filePrefixes.length)];
    const type = fileTypes[Math.floor(Math.random() * fileTypes.length)];
    const status = fileStatuses[Math.floor(Math.random() * fileStatuses.length)];

    // Random upload date in 2024–2025
    const uploadYear = 2024 + Math.floor(Math.random() * 2);
    const uploadedDate = new Date(
      uploadYear,
      Math.floor(Math.random() * 12),
      Math.floor(Math.random() * 28) + 1
    );

    // For simplicity, approved/rejected dates are within 0–10 days after upload
    const offsetDays = Math.floor(Math.random() * 10);
    const approvedDate = new Date(uploadedDate);
    approvedDate.setDate(uploadedDate.getDate() + offsetDays);

    const rejectedDate = new Date(uploadedDate);
    rejectedDate.setDate(uploadedDate.getDate() + offsetDays);

    // Assign users only when relevant
    const approvedUser = status === "Approved"
      ? userNames[Math.floor(Math.random() * userNames.length)]
      : "";
    const rejectedUser = status === "Rejected"
      ? userNames[Math.floor(Math.random() * userNames.length)]
      : "";

    return {
      id: i + 1,
      fileName: `${prefix}${i + 1}.${type.toLowerCase()}`,
      fileType: type,
      fileSize: `${(Math.random() * 3 + 1).toFixed(1)} MB`,
      fileStatus: status,
      uploadedDate,
      approvedDate,
      approvedUser,
      rejectedDate,
      rejectedUser,
    };
  });
};

export const useSubmitedFilesController = () => {
  // 1) Base data and grid state
  const [data] = useState<FileRecord[]>(generateInitialData());
  const [select, setSelect] = useState<SelectDescriptor>({});
  const [sort, setSort] = useState<SortDescriptor[]>([]);
  const [skip, setSkip] = useState(0);
  const [take, setTake] = useState(15);
  const [filter, setFilter] = useState<CompositeFilterDescriptor>({
    logic: "and",
    filters: [],
  });

  // 2) Filtering → Sorting → Paging
  const filteredData = useMemo(
    () => (filter.filters?.length ? filterBy(data, filter) : data),
    [data, filter]
  );

  const sortedData = useMemo(
    () => (sort.length ? orderBy(filteredData, sort) : filteredData),
    [filteredData, sort]
  );

  const currentPageData = useMemo(
    () => sortedData.slice(skip, skip + take),
    [sortedData, skip, take]
  );

  const total = sortedData.length;

  // 3) Dropdown option lists for column menus
  const fileTypeOptions = Array.from(
    new Set(currentPageData.map((item) => item.fileType))
  ).map((fileType) => ({ fileType }));

  const fileStatusOptions = Array.from(
    new Set(currentPageData.map((item) => item.fileStatus))
  ).map((fileStatus) => ({ fileStatus }));

  const approvedUserOptions = Array.from(
    new Set(currentPageData.map((item) => item.approvedUser).filter(Boolean))
  ).map((approvedUser) => ({ approvedUser }));

  const rejectedUserOptions = Array.from(
    new Set(currentPageData.map((item) => item.rejectedUser).filter(Boolean))
  ).map((rejectedUser) => ({ rejectedUser }));

  // 4) Selection state
  const headerSelectionValue = useMemo(
    () =>
      currentPageData.length > 0 &&
      currentPageData.every((item) => !!select[item.id]),
    [currentPageData, select]
  );

  const selectedIds = useMemo(
    () =>
      Object.entries(select)
        .filter(([, isSelected]) => isSelected)
        .map(([id]) => parseInt(id, 10)),
    [select]
  );

  // 5) Event handlers
  const onSelectionChange = (e: GridSelectionChangeEvent) => {
    setSelect(e.select);
  };

  const onHeaderSelectionChange = (e: GridHeaderSelectionChangeEvent) => {
    setSelect(e.select);
  };

  const onSortChange = (e: { sort: SortDescriptor[] }) => {
    setSort(e.sort);
  };

  const onPageChange = (e: GridPageChangeEvent) => {
    setSkip(e.page.skip);
    setTake(e.page.take);
  };

  const onFilterChange = (e: {
    filter: CompositeFilterDescriptor | null;
  }) => {
    setFilter(e.filter ?? { logic: "and", filters: [] });
  };

  const handleDownload = (ids: number[]) => {
    logger.info("Downloading submitted files with IDs:", ids);
    // TODO: implement real download logic
  };

  return {
    currentPageData,
    total,
    skip,
    take,
    sort,
    filter,
    fileTypeOptions,
    fileStatusOptions,
    approvedUserOptions,
    rejectedUserOptions,
    headerSelectionValue,
    selectedIds,
    onSelectionChange,
    onHeaderSelectionChange,
    onSortChange,
    onPageChange,
    onFilterChange,
    handleDownload,
  };
};
