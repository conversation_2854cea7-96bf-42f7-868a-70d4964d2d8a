// Compatible with @progress/kendo-theme-default v.11.0.2

$tb-kendo-color-light: #757575;
$tb-kendo-color-on-light: white;
$tb-kendo-color-on-app-surface: #424242;
$tb-kendo-color-surface-alt: #ffffff;
$tb-kendo-color-primary: #1274AC;
$tb-kendo-color-surface: #fafafa;
$tb-kendo-color-primary-hover: #116fa5;
$tb-kendo-color-base: #f5f5f5;
$tb-kendo-color-on-base: #424242;
$tb-kendo-color-base-hover: #ebebeb;
$tb-kendo-color-base-active: #d8d8d8;
$tb-kendo-color-on-primary: white;
$tb-kendo-color-subtle: #666666;
$tb-kendo-color-error: #D51923;
$tb-kendo-color-base-subtle: #ebebeb;
$tb-kendo-color-base-subtle-hover: #e1e1e1;
$tb-kendo-color-base-on-surface: #424242;
$tb-kendo-color-app-surface: #ffffff;
$tb-kendo-color-primary-subtle: #d0e3ee;
$tb-kendo-color-primary-active: #106697;
$tb-kendo-color-base-emphasis: #c2c2c2;
$tb-kendo-color-success: #278200;
$tb-kendo-color-info: #0058E9;
$tb-kendo-color-base-subtle-active: #d5d5d5;
$tb-kendo-color-base-on-subtle: #424242;
$tb-kendo-color-primary-subtle-hover: #accee2;
$tb-kendo-color-primary-subtle-active: #89bad6;
$tb-kendo-color-primary-emphasis: #4d97c1;
$tb-kendo-color-primary-on-subtle: #06293c;
$tb-kendo-color-primary-on-surface: #0e5781;
$tb-kendo-color-secondary-subtle: #e3e3e3;
$tb-kendo-color-secondary-subtle-hover: #cfcfcf;
$tb-kendo-color-secondary-subtle-active: #bababa;
$tb-kendo-color-secondary: #757575;
$tb-kendo-color-secondary-hover: #707070;
$tb-kendo-color-secondary-active: #676767;
$tb-kendo-color-secondary-emphasis: #989898;
$tb-kendo-color-secondary-on-subtle: #292929;
$tb-kendo-color-on-secondary: white;
$tb-kendo-color-secondary-on-surface: #585858;
$tb-kendo-color-tertiary-subtle: #cce5e6;
$tb-kendo-color-tertiary-subtle-hover: #a6d2d4;
$tb-kendo-color-tertiary-subtle-active: #80bfc2;
$tb-kendo-color-tertiary: #007F84;
$tb-kendo-color-tertiary-hover: #007a7f;
$tb-kendo-color-tertiary-active: #007074;
$tb-kendo-color-tertiary-emphasis: #409fa3;
$tb-kendo-color-tertiary-on-subtle: #002c2e;
$tb-kendo-color-on-tertiary: white;
$tb-kendo-color-tertiary-on-surface: #005f63;
$tb-kendo-color-info-subtle: #ccdefb;
$tb-kendo-color-info-subtle-hover: #a6c5f7;
$tb-kendo-color-info-subtle-active: #80acf4;
$tb-kendo-color-info-hover: #0054e0;
$tb-kendo-color-info-active: #004dcd;
$tb-kendo-color-info-emphasis: #4082ef;
$tb-kendo-color-info-on-subtle: #001f52;
$tb-kendo-color-on-info: white;
$tb-kendo-color-info-on-surface: #0042af;
$tb-kendo-color-success-subtle: #d4e6cc;
$tb-kendo-color-success-subtle-hover: #b3d3a6;
$tb-kendo-color-success-subtle-active: #93c180;
$tb-kendo-color-success-hover: #257d00;
$tb-kendo-color-success-active: #227200;
$tb-kendo-color-success-emphasis: #5da140;
$tb-kendo-color-success-on-subtle: #0e2e00;
$tb-kendo-color-on-success: white;
$tb-kendo-color-success-on-surface: #1d6200;
$tb-kendo-color-warning-subtle: #fdedcc;
$tb-kendo-color-warning-subtle-hover: #fce0a6;
$tb-kendo-color-warning-subtle-active: #fad380;
$tb-kendo-color-warning: #F5A600;
$tb-kendo-color-warning-hover: #eb9f00;
$tb-kendo-color-warning-active: #d89200;
$tb-kendo-color-warning-emphasis: #f8bc40;
$tb-kendo-color-warning-on-subtle: #563a00;
$tb-kendo-color-on-warning: white;
$tb-kendo-color-warning-on-surface: #b87d00;
$tb-kendo-color-error-subtle: #f7d1d3;
$tb-kendo-color-error-subtle-hover: #f0afb2;
$tb-kendo-color-error-subtle-active: #ea8c91;
$tb-kendo-color-error-hover: #cc1822;
$tb-kendo-color-error-active: #bb161f;
$tb-kendo-color-error-emphasis: #e0535a;
$tb-kendo-color-error-on-subtle: #4b090c;
$tb-kendo-color-on-error: white;
$tb-kendo-color-error-on-surface: #a0131a;
$tb-kendo-color-light-subtle: #e3e3e3;
$tb-kendo-color-light-subtle-hover: #cfcfcf;
$tb-kendo-color-light-subtle-active: #bababa;
$tb-kendo-color-light-hover: #707070;
$tb-kendo-color-light-active: #676767;
$tb-kendo-color-light-emphasis: #989898;
$tb-kendo-color-light-on-subtle: #292929;
$tb-kendo-color-light-on-surface: #585858;
$tb-kendo-color-series-a: #1274AC;
$tb-kendo-color-series-a-bold: #0e5781;
$tb-kendo-color-series-a-bolder: #093a56;
$tb-kendo-color-series-a-subtle: #4d97c1;
$tb-kendo-color-series-a-subtler: #89bad6;
$tb-kendo-color-series-b: #FFE162;
$tb-kendo-color-series-b-bold: #bfa94a;
$tb-kendo-color-series-b-bolder: #807131;
$tb-kendo-color-series-b-subtle: #ffe989;
$tb-kendo-color-series-b-subtler: #fff0b1;
$tb-kendo-color-series-c: #4CD180;
$tb-kendo-color-series-c-bold: #399d60;
$tb-kendo-color-series-c-bolder: #266940;
$tb-kendo-color-series-c-subtle: #79dda0;
$tb-kendo-color-series-c-subtler: #a6e8c0;
$tb-kendo-color-series-d: #0074e6;
$tb-kendo-color-series-d-bold: #0057ad;
$tb-kendo-color-series-d-bolder: #003a73;
$tb-kendo-color-series-d-subtle: #4097ec;
$tb-kendo-color-series-d-subtler: #80baf3;
$tb-kendo-color-series-e: #9B4FE6;
$tb-kendo-color-series-e-bold: #743bad;
$tb-kendo-color-series-e-bolder: #4e2873;
$tb-kendo-color-series-e-subtle: #b47bec;
$tb-kendo-color-series-e-subtler: #cda7f3;
$tb-kendo-color-series-f: #FF80AC;
$tb-kendo-color-series-f-bold: #bf6081;
$tb-kendo-color-series-f-bolder: #804056;
$tb-kendo-color-series-f-subtle: #ffa0c1;
$tb-kendo-color-series-f-subtler: #ffc0d6;


    $tb-kendo-font-family: inherit;
    $tb-kendo-font-size: 14px;
    $tb-kendo-font-weight-normal: 400;
    $tb-kendo-line-height: 1.4285714286;
    $enable-gradients: true;

$tb-typography: (
  kendo-default-typography: (
      font-family: 'inherit',
      font-size: 14px,
      font-weight: 400,
      line-height: 1.4285714286,
        ),
);

@mixin typography-classes ($typography) {
  @each $selector, $property in $typography {
    &-#{$selector} {
      @each $propName, $propValue in $property {
        #{$propName}: #{$propValue};
      }
    } &-#{$selector}-important {
      @each $propName, $propValue in $property {
        #{$propName}: $propValue !important;
      }
    }
  }
}

$tb-effects: (
  tb-internal-none-effects: (
      box-shadow: (none), filter: (none), backdrop-filter: (none),  ),
);

@mixin effects-classes ($effects) {
  @each $selector, $property in $effects {
    &-#{$selector} {
      @each $propName, $propValue in $property {
        #{$propName}: $propValue;
      }
    } &-#{$selector}-important {
      @each $propName, $propValue in $property {
        #{$propName}: $propValue !important;
      }
    }
  }
}