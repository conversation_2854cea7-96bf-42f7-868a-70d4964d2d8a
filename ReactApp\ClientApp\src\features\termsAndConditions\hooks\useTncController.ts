import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/store/hooks';
import { useAcceptTncMutation, useEvaluateTncQuery, useFetchTncDocumentQuery } from '@/api/tncApiSlice';


export const useTncController = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const pdfViewerRef = useRef<any>(null);
    const [pdfBlobUrl, setPdfBlobUrl] = useState<string>("");
    const [pdfDocument, setPdfDocument] = useState<any>(null);
    const tncDocument = useAppSelector((state) => state.app.tnc);

    const redirectPath = location.state?.from || '/';

    const { refetch: tncRefetch, isFetching: tncLoading } = useEvaluateTncQuery();
    const [acceptTnc, { isLoading: isAccepting }] = useAcceptTncMutation();


    const {
        data: pdfBlob,
        isLoading: isPdfLoading,
        refetch: refetchPdf,
    } = useFetchTncDocumentQuery(tncDocument.document?.documentUrl || "", {
        skip: !tncDocument.document?.documentUrl,
    });



    const onDocumentLoad = (event: any) => {
        setPdfDocument(event.target);
    };

    const handleAgree = async () => {
        let messageText;
        try {
            await acceptTnc({ termAndConditionId: tncDocument.document!.termAndConditionId, triggerType: tncDocument?.document?.triggerType || "" }).unwrap();
            navigate(redirectPath);
        } catch (err: any) {
            messageText = 'Terms & Conditions acceptance failed';
            if (err?.status >= 400 && err?.status < 500 && err?.data?.message) {
                messageText = err?.data?.message;
            }
            //errorNotification([''], messageText);
        }
    };

    const clickToolbarButtonByTitle = (title: string) => {
        const button = document.querySelector(`button[title="${title}"]`) as HTMLElement;
        button?.click();
    };

    useEffect(() => {
        if (tncDocument.isValidated || !tncDocument.document) {
            navigate(redirectPath);
        }
    }, [tncDocument, navigate, redirectPath]);

    useEffect(() => {
        if (pdfBlob) {
            const blobUrl = URL.createObjectURL(pdfBlob);
            setPdfBlobUrl(blobUrl);

            return () => {
                URL.revokeObjectURL(blobUrl);
            };
        }
    }, [pdfBlob]);



    return {
        tncDocument,
        pdfViewerRef,
        pdfDocument,
        setPdfDocument,
        tncRefetch,
        tncLoading,
        isAccepting,
        onDocumentLoad,
        handleAgree,
        clickToolbarButtonByTitle,
        pdfBlobUrl,
        isPdfLoading,
        refetchPdf,
    };
};
