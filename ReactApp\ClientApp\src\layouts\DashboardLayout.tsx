import { Outlet } from 'react-router-dom';
import { Sidebar } from '@/components/dashboardLayout/Sidebar';
import { TopBar } from '@/components/dashboardLayout/TopBar';
import { useSidebarState } from '@/hooks/useSidebarState';
import LoaderComponent from '@/components/Loader/Loader';
// import { Footer } from '@/components/dashboardLayout/Footer';
import './DashboardLayout.scss';

const DashboardLayout = ({ children }: { children?: React.ReactNode }) => {
  const { isLoading, error, isReady } = useSidebarState();

  return (
    <div className="dashboard-layout">
      <TopBar />

      <div className="dashboard-layout__body">
        <Sidebar />

        <div className="dashboard-layout__main">
          <main className="dashboard-layout__content">
            {/* Show loading state while sidebar is loading */}
            {isLoading && (
              <div className="dashboard-layout__loading">
                <LoaderComponent />
                <p>Loading dashboard...</p>
              </div>
            )}

            {/* Hide content entirely if there's an error */}
            {error && (
              <div className="dashboard-layout__error">
                <p>Unable to load dashboard. Please try again later.</p>
              </div>
            )}

            {/* Show main content only when sidebar is ready */}
            {isReady && (children || <Outlet />)}
          </main>
          {/* <Footer /> */}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
