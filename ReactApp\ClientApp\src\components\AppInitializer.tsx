import { useEffect, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { DiscoveryClient } from "@/App";
import {
  setBaseUrls,
  setDiscoveryLoading,
  setDiscoveryError,
  selectDiscoveryStatus,
} from "@/store/discoverySlice";
import logger from "@/utils/logger";

const AppInitializer = () => {
  const dispatch = useAppDispatch();

  const token = useAppSelector((state) => state.okta.idToken);
  const tenant = useAppSelector(
    (state) => state.okta.userInfo?.tenantCode?.[0],
  );
  const discoveryStatus = useAppSelector(selectDiscoveryStatus);

  const fetchDiscovery = useCallback(async () => {
    dispatch(setDiscoveryLoading());
    logger.info("AppInitializer: Fetching base URLs for tenant", { tenant });

    try {
      const discoveryEndpoints = await DiscoveryClient.getBaseURL(
        "AP_AUTH_TOKEN",
        {
          TenantCode: tenant,
        },
      );

      if (Array.isArray(discoveryEndpoints)) {
        dispatch(setBaseUrls(discoveryEndpoints));
      } else {
        logger.error("AppInitializer: Invalid discovery result");
        dispatch(setDiscoveryError());
      }
    } catch (error) {
      logger.error("AppInitializer: Discovery service failed", { error });
      dispatch(setDiscoveryError());
    }
  }, [dispatch, tenant]);

  useEffect(() => {
    if (token && tenant && discoveryStatus === "idle") {
      fetchDiscovery();
    }
  }, [token, tenant, discoveryStatus, fetchDiscovery]);

  return null;
};

export default AppInitializer;
