import { useState } from "react";
import {
  homeIcon,
  fileTxtIcon,
  folderIcon,
  folderOpenIcon,
  chevronLeftIcon,
  chevronRightIcon,
  chevronUpIcon,
  chevronDownIcon,
  gearIcon,
} from "@progress/kendo-svg-icons";
import { Button } from "@progress/kendo-react-buttons";
import { SvgIcon } from "@progress/kendo-react-common";
import "./Sidebar.scss";
import { ROUTES } from "@/constants/routes";
import { useNavigate, useLocation } from "react-router-dom";

export const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const [submittedOpen, setSubmittedOpen] = useState(true);
  const [publishedOpen, setPublishedOpen] = useState(true);

  const toggleCollapsed = () => setCollapsed((prev) => !prev);
  const toggleSubmitted = () => setSubmittedOpen((prev) => !prev);
  const togglePublished = () => setPublishedOpen((prev) => !prev);

  // Check if current route is portal-controls
  const isPortalControlsActive = location.pathname === ROUTES.PORTAL_CONTROLS;

  return (
    <aside className={`sidebar ${collapsed ? "sidebar--collapsed" : ""}`}>
      <div className="sidebar__container">
        <div className="sidebar__scrollable">
          <div className="sidebar__section">
            <div className="sidebar__item" onClick={toggleCollapsed}>
              <div className="sidebar__item-left">
                <SvgIcon icon={homeIcon} className="sidebar__icon" />
                {!collapsed && <span className="sidebar__label">Home</span>}
              </div>
              {!collapsed && (
                <Button fillMode="flat" className="sidebar__collapse">
                  <SvgIcon
                    icon={collapsed ? chevronRightIcon : chevronLeftIcon}
                    className="sidebar__icon"
                  />
                </Button>
              )}
            </div>
          </div>

          <div className="sidebar__section">
            <div className="sidebar__item" onClick={toggleSubmitted}>
              <div className="sidebar__item-left">
                <SvgIcon icon={fileTxtIcon} className="sidebar__icon" />
                {!collapsed && (
                  <span className="sidebar__label">Submitted Files</span>
                )}
              </div>
              {!collapsed && (
                <SvgIcon
                  icon={submittedOpen ? chevronUpIcon : chevronDownIcon}
                  className="sidebar__caret"
                />
              )}
            </div>
            {!collapsed && submittedOpen && (
              <ul className="sidebar__submenu">
                <li>
                  <SvgIcon icon={folderIcon} className="sidebar__subicon" />
                  Engagement Letters
                </li>
                <li>
                  <SvgIcon icon={folderIcon} className="sidebar__subicon" />
                  Audit Evidence
                </li>
              </ul>
            )}
          </div>

          <div className="sidebar__section">
            <div className="sidebar__item" onClick={togglePublished}>
              <div className="sidebar__item-left">
                <SvgIcon icon={folderOpenIcon} className="sidebar__icon" />
                {!collapsed && (
                  <span className="sidebar__label">Published Files</span>
                )}
              </div>
              {!collapsed && (
                <div className="sidebar__right-icons">
                  <SvgIcon icon={gearIcon} className="sidebar__gear" />
                  <SvgIcon
                    icon={publishedOpen ? chevronUpIcon : chevronDownIcon}
                    className="sidebar__caret"
                  />
                </div>
              )}
            </div>
            {!collapsed && publishedOpen && (
              <ul className="sidebar__submenu">
                <li>
                  <SvgIcon icon={folderIcon} className="sidebar__subicon" />
                  Final Reports
                </li>
                <li>
                  <SvgIcon icon={folderIcon} className="sidebar__subicon" />
                  Shared Letters
                </li>
              </ul>
            )}
          </div>
        </div>

        <div onClick={() => navigate(ROUTES.PORTAL_CONTROLS)} className={`sidebar__footer ${isPortalControlsActive ? 'sidebar__footer--active' : ''}`}>
          <Button
            fillMode="flat"
            className="sidebar__footer-link"
            disabled={collapsed}
          >
            <span className="sidebar__footer-content">
              <SvgIcon icon={gearIcon} className="sidebar__footer-icon" />
              {!collapsed && (
                <span className="sidebar__footer-text">Portal Controls</span>
              )}
            </span>
          </Button>
        </div>
      </div>
    </aside>
  );
};
