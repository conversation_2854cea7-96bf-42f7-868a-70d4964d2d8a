import { useState } from "react";
import {
  homeIcon,
  fileTxtIcon,
  chevronLeftIcon,
  chevronRightIcon,
  gearIcon,
} from "@progress/kendo-svg-icons";
import { Button } from "@progress/kendo-react-buttons";
import { SvgIcon } from "@progress/kendo-react-common";
import "./Sidebar.scss";
import { ROUTES } from "@/constants/routes";
import { useNavigate, useLocation } from "react-router-dom";
import { useSidebarState } from "@/hooks/useSidebarState";
import type { MenuItem } from "@/types/Dashboard";

// Map menu item keys to routes
const getRouteForMenuItem = (menuKey: string): string => {
  const routeMap: Record<string, string> = {
    'SubmittedFiles': ROUTES.SUBMITTED_FILES,
    'PublishedFiles': ROUTES.PUBLISHED_FILES,
    'PortalControls': ROUTES.PORTAL_CONTROLS,
    'SampleFeature': ROUTES.SAMPLE_FEATURE,
  };

  return routeMap[menuKey] || ROUTES.DASHBOARD;
};

export const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);

  const { menuItems, isLoading, error } = useSidebarState();

  const toggleCollapsed = () => setCollapsed((prev) => !prev);

  // Check if current route is portal-controls
  const isPortalControlsActive = location.pathname === ROUTES.PORTAL_CONTROLS;

  // Check if a menu item is currently active
  const isMenuItemActive = (menuKey: string): boolean => {
    const route = getRouteForMenuItem(menuKey);
    return location.pathname === route;
  };

  // Handle menu item click
  const handleMenuItemClick = (menuItem: MenuItem) => {
    const route = getRouteForMenuItem(menuItem.key);
    navigate(route);
  };

  // Hide sidebar entirely if there's an error fetching menu items
  if (error) {
    return null;
  }

  // Sort menu items by order field
  const sortedMenuItems = menuItems ? [...menuItems].sort((a, b) => a.order - b.order) : [];

  return (
    <aside className={`sidebar ${collapsed ? "sidebar--collapsed" : ""}`}>
      <div className="sidebar__container">
        <div className="sidebar__scrollable">
          <div className="sidebar__section">
            <div className="sidebar__item" onClick={toggleCollapsed}>
              <div className="sidebar__item-left">
                <SvgIcon icon={homeIcon} className="sidebar__icon" />
                {!collapsed && <span className="sidebar__label">Home</span>}
              </div>
              {!collapsed && (
                <Button fillMode="flat" className="sidebar__collapse">
                  <SvgIcon
                    icon={collapsed ? chevronRightIcon : chevronLeftIcon}
                    className="sidebar__icon"
                  />
                </Button>
              )}
            </div>
          </div>

          {/* Loading state */}
          {isLoading && (
            <div className="sidebar__section">
              <div className="sidebar__item">
                <div className="sidebar__item-left">
                  <SvgIcon icon={fileTxtIcon} className="sidebar__icon" />
                  {!collapsed && <span className="sidebar__label">Loading...</span>}
                </div>
              </div>
            </div>
          )}

          {/* Dynamic menu items */}
          {!isLoading && sortedMenuItems.map((menuItem: MenuItem) => {
            const isActive = isMenuItemActive(menuItem.key);
            return (
              <div key={menuItem.id} className="sidebar__section">
                <div
                  className={`sidebar__item ${isActive ? 'sidebar__item--active' : ''}`}
                  onClick={() => handleMenuItemClick(menuItem)}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="sidebar__item-left">
                    <SvgIcon icon={fileTxtIcon} className="sidebar__icon" />
                    {!collapsed && (
                      <span className="sidebar__label">{menuItem.name}</span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div onClick={() => navigate(ROUTES.PORTAL_CONTROLS)} className={`sidebar__footer ${isPortalControlsActive ? 'sidebar__footer--active' : ''}`}>
          <Button
            fillMode="flat"
            className="sidebar__footer-link"
            disabled={collapsed}
          >
            <span className="sidebar__footer-content">
              <SvgIcon icon={gearIcon} className="sidebar__footer-icon" />
              {!collapsed && (
                <span className="sidebar__footer-text">Portal Controls</span>
              )}
            </span>
          </Button>
        </div>
      </div>
    </aside>
  );
};
