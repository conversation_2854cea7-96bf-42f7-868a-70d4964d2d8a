import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "./store";

import type { WEBEndpointType } from "@iris/discovery.fe.client";
import DISCOVERY_STATUS from "@/constants/discoveryStatus";
interface DiscoveryState {
  baseUrls: WEBEndpointType[];
  status: (typeof DISCOVERY_STATUS)[keyof typeof DISCOVERY_STATUS];
}

const initialState: DiscoveryState = {
  baseUrls: [],
  status: DISCOVERY_STATUS.IDLE,
};

const discoverySlice = createSlice({
  name: "discovery",
  initialState,
  reducers: {
    setBaseUrls: (state, action: PayloadAction<WEBEndpointType[]>) => {
      state.baseUrls = action.payload;
      state.status = DISCOVERY_STATUS.SUCCESS;
    },
    setDiscoveryLoading: (state) => {
      state.status = DISCOVERY_STATUS.LOADING;
    },
    setDiscoveryError: (state) => {
      state.status = DISCOVERY_STATUS.ERROR;
    },
    clearBaseUrls: (state) => {
      state.baseUrls = [];
      state.status = DISCOVERY_STATUS.IDLE;
    },
  },
});

export const {
  setBaseUrls,
  clearBaseUrls,
  setDiscoveryLoading,
  setDiscoveryError,
} = discoverySlice.actions;

export const selectBaseUrls = (state: RootState) => state.discovery.baseUrls;
export const selectDiscoveryStatus = (state: RootState) =>
  state.discovery.status;

export default discoverySlice.reducer;
