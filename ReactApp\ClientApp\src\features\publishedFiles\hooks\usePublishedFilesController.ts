import { useState, useMemo } from "react";
import { orderBy, filterBy } from "@progress/kendo-data-query";
import type {
  SortDescriptor,
  CompositeFilterDescriptor,
} from "@progress/kendo-data-query";
import type {
  GridSelectionChangeEvent,
  GridHeaderSelectionChangeEvent,
  GridPageChangeEvent,
} from "@progress/kendo-react-grid";
import type { SelectDescriptor } from "@progress/kendo-react-data-tools";

export interface FileRecord {
  id: number;
  fileName: string;
  fileType: string;
  fileSize: string;
  year: number;
  publishedDate: Date;
  primaryCategory: string;
  secondaryCategory: string;
}

// Generate mock data
const generateInitialData = (): FileRecord[] => {
  const filePrefixes = ["Report_", "Tax_File_", "Income_Tax_"];
  const fileTypes = ["PDF", "CSV", "JPEG"];
  const years = [2021, 2022, 2023, 2024, 2025];
  const primaryCategories = [
    "Category A",
    "Category B",
    "Category C",
    "Category D",
    "Category E",
  ];
  const secondaryCategories = ["Category Z", "Category Y", "Category X"];

  return Array.from({ length: 30 }).map((_, i) => {
    const randomYear = years[Math.floor(Math.random() * years.length)];
    const randomPrefix =
      filePrefixes[Math.floor(Math.random() * filePrefixes.length)];
    const randomType = fileTypes[Math.floor(Math.random() * fileTypes.length)];
    const randomPrimary =
      primaryCategories[Math.floor(Math.random() * primaryCategories.length)];
    const randomSecondary =
      secondaryCategories[
      Math.floor(Math.random() * secondaryCategories.length)
      ];

    return {
      id: i + 1,
      fileName: `${randomPrefix}${i + 1}.${randomType.toLowerCase()}`,
      fileType: randomType,
      fileSize: `${(Math.random() * 3 + 1).toFixed(1)} MB`,
      year: randomYear,
      publishedDate: new Date(
        randomYear,
        Math.floor(Math.random() * 12),
        Math.floor(Math.random() * 28) + 1
      ),
      primaryCategory: randomPrimary,
      secondaryCategory: randomSecondary,
    };
  });
};

export const usePublishedFilesController = () => {
  // 1) Base data and grid state
  const [data] = useState<FileRecord[]>(generateInitialData());
  const [select, setSelect] = useState<SelectDescriptor>({});
  const [sort, setSort] = useState<SortDescriptor[]>([]);
  const [skip, setSkip] = useState(0);
  const [take, setTake] = useState(15);
  const [filter, setFilter] = useState<CompositeFilterDescriptor>({
    logic: "and",
    filters: [],
  });

  // 2) Filter → Sort → Page
  const filteredData = useMemo(() => {
    return filter.filters && filter.filters.length
      ? filterBy(data, filter)
      : data;
  }, [data, filter]);

  const sortedData = useMemo(
    () => (sort.length ? orderBy(filteredData, sort) : filteredData),
    [filteredData, sort]
  );

  const currentPageData = useMemo(
    () => sortedData.slice(skip, skip + take),
    [sortedData, skip, take]
  );

  const total = sortedData.length;

  // 3) Multi-select dropdown option lists
  const fileTypeOptions = Array.from(
    new Set(currentPageData.map(item => item.fileType))
  ).map(ft => ({ fileType: ft }));

  const yearOptions = Array.from(
    new Set(currentPageData.map(item => item.year))
  ).map(yr => ({ year: yr.toString() }));

  const primaryCategoryOptions = Array.from(
    new Set(currentPageData.map(item => item.primaryCategory))
  ).map(pc => ({ primaryCategory: pc }));

  const secondaryCategoryOptions = Array.from(
    new Set(currentPageData.map(item => item.secondaryCategory))
  ).map(sc => ({ secondaryCategory: sc }));


  // 4) Selection state
  const headerSelectionValue = useMemo(
    () =>
      currentPageData.length > 0 &&
      currentPageData.every((item) => !!select[item.id]),
    [currentPageData, select]
  );

  const selectedIds = useMemo(
    () =>
      Object.entries(select)
        .filter(([, isSelected]) => isSelected)
        .map(([id]) => parseInt(id, 10)),
    [select]
  );

  // 5) Event handlers
  const onSelectionChange = (e: GridSelectionChangeEvent) => {
    setSelect(e.select);
  };

  const onHeaderSelectionChange = (e: GridHeaderSelectionChangeEvent) => {
    setSelect(e.select);
  };

  const onSortChange = (e: { sort: SortDescriptor[] }) => {
    setSort(e.sort);
  };

  const onPageChange = (e: GridPageChangeEvent) => {
    setSkip(e.page.skip);
    setTake(e.page.take);
  };

  const onFilterChange = (e: { filter: CompositeFilterDescriptor | null }) => {
    setFilter(e.filter ?? { logic: "and", filters: [] });
  };

  return {
    currentPageData,
    total,
    skip,
    take,
    sort,
    filter,
    fileTypeOptions,
    yearOptions,
    primaryCategoryOptions,
    secondaryCategoryOptions,
    select,
    headerSelectionValue,
    selectedIds,
    onSelectionChange,
    onHeaderSelectionChange,
    onSortChange,
    onPageChange,
    onFilterChange,
  };
};
