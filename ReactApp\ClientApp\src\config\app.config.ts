import type { EnvConfig } from "@/types/global";

if (!window._env_) {
  throw new Error(
    "Environment variables not found. Make sure 'env-config.js' is loaded before the app script.",
  );
}

const env = window._env_ as EnvConfig;

const requiredKeys: (keyof EnvConfig)[] = [
  "REACT_APP_OKTA_CLIENT_ID",
  "REACT_APP_BASE_URL",
  "REACT_APP_LOG_LEVEL",
  "REACT_APP_DISCOVERY_URL",
];

for (const key of requiredKeys) {
  if (!env[key]) {
    throw new Error(`Missing required environment variable: ${key}`);
  }
}

export type AppConfig = {
  baseUrl: string;
  discoveryURL: string;
  logLevel: string;
  clientId: string;
  applicationConfig: {
    appTimeZone: string;
    paginationConfig: {
      pageSizeOptions: number[];
      defaultSize: number;
    };
  };
  roles: Record<string, string>;
  featureFlags: {
    api: {
      GET_PUBLISHED_FILES: boolean;
    };
    ui: {
      SHOW_SAMPLE_TEMPLATE: boolean;
    };
  };
  api: {
    [47]: {
      evaluateTnc: string,
      acceptTnc: string,
    };
  };
  temporary: {
    db: string;
    authorization: string;
  };
};

const defaultAppConfig: AppConfig = {
  baseUrl: env.REACT_APP_BASE_URL,
  discoveryURL: env.REACT_APP_DISCOVERY_URL,
  logLevel: env.REACT_APP_LOG_LEVEL,
  clientId: env.REACT_APP_OKTA_CLIENT_ID,
  applicationConfig: {
    appTimeZone: "UTC",
    paginationConfig: {
      pageSizeOptions: [10, 20, 50],
      defaultSize: 10,
    },
  },
  roles: {
    system_admin: "System Admin",
    admin: "Admin",
    manager: "Manager",
    officer: "Officer",
  },
  featureFlags: {
    api: { // mock API flags
      GET_PUBLISHED_FILES: false,
    },
    ui: {
      SHOW_SAMPLE_TEMPLATE: true,
    },
  },
  api: {
    [47]: {
      evaluateTnc: `api/terms-and-conditions/evaluate`,
      acceptTnc: `api/terms-and-conditions/accept`,
    }
  },
  temporary: {
    db: "NTB1",
    authorization:
      "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  },
};

export default defaultAppConfig;
