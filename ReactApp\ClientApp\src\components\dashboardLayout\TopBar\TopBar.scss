.topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #24303b; // var(--kendo-color-custom-bar-background)
  color: var(--kendo-color-base-text);
  height: 60px;
  padding: 0 1.5rem;
  border-bottom: 5px solid var(--kendo-color-series-a-subtle);

  &__left,
  &__right {
    display: flex;
    align-items: center;
  }

  &__left {
    gap: 0.75rem;
  }

  &__right {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  &__logo-image {
    height: 30px;
    object-fit: contain;
  }

  &__divider {
    width: 1px;
    height: 24px;
    background-color: var(--kendo-color-on-light);
  }

  &__title {
    font-size: 0.95rem;
    color: var(--kendo-color-on-light);
  }

  &__company-dropdown {
    &.k-dropdownlist {
      width: 220px;
      height: 60px;
      border: 1px solid var(--kendo-color-primary);
      border-radius: 0 !important;

      .k-picker {
        width: 100%;
        height: 100%;
        background-color: var(--kendo-color-primary);
        border: none;
        border-radius: 0 !important;
        display: flex;
        align-items: center;
      }
    }

    .k-input-inner,
    .k-input-value-text,
    .k-input-button {
      background-color: var(--kendo-color-primary);
      color: var(--kendo-color-on-primary);
      font-weight: 500;
    }
  }

  &__bell-button {
    color: var(--kendo-color-on-light) !important;

    .k-svg-icon {
      color: var(--kendo-color-on-light) !important;
      fill: var(--kendo-color-on-light) !important;
    }
  }

  &__icon {
    width: 20px;
    height: 20px;
    color: var(--kendo-color-on-light);
    cursor: pointer;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  /*
     Avatar + Dropdown
   */
  &__avatar-container {
    position: relative;
    cursor: pointer;

    .topbar__avatar {
      background-color: var(--kendo-color-primary);
      border-radius: 50%;
      color: var(--kendo-color-on-primary);
      font-weight: bold;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: var(--kendo-color-primary-hover);
      }
    }

    .topbar__avatar-dropdown {
      position: absolute;
      top: calc(100% + 6px);
      right: 0;
      background: #ffffff;
      min-width: 140px;
      box-shadow: var(--kendo-shadow-depth-3);
      border-radius: 4px;
      overflow: hidden;
      z-index: 1000;
      display: flex;
      flex-direction: column;

      .topbar__logout-button {
        color: var(--kendo-color-base-text) !important;
        font-size: 0.9rem;
        width: 100%;
        text-align: left;
        justify-content: flex-start;
        padding: 8px 12px;
        border-radius: 0;
        background-color: transparent;

        &:hover {
          background-color: var(--kendo-color-series-a-subtle);
          color: var(--kendo-color-primary);
        }

        .k-svg-icon {
          margin-right: 6px;
        }
      }
    }
  }

  &__overflow-button {
    display: none;

    .k-button {
      min-width: 36px;
      height: 36px;
      padding: 0;
      border-radius: 4px;
      background-color: transparent;
      color: var(--kendo-color-on-light);

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  &__popup-menu {
    background: #ffffff;
    padding: 0.5rem 0;
    box-shadow: var(--kendo-shadow-depth-3);
    border-radius: 4px;
    min-width: 160px;

    ul {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        padding: 8px 16px;
        cursor: pointer;
        font-size: 0.9rem;
        color: var(--kendo-color-base-text);
        display: flex;
        align-items: center;
        gap: 6px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: var(--kendo-color-series-a-subtle);
          color: var(--kendo-color-primary);
        }

        &.logout-item {
          color: #d9534f;

          &:hover {
            background-color: #f8d7da;
            color: #c82333;
          }
        }
      }
    }
  }

  // Responsive behavior
  @media (max-width: 768px) {
    &__company-dropdown,
    &__icon,
    &__avatar-container,
    &__logout-button {
      display: none;
    }

    &__overflow-button {
      display: block;
    }
  }
}
