import { Button } from "@progress/kendo-react-buttons";
import { printIcon, downloadIcon, rotateIcon } from "@progress/kendo-svg-icons";
import "./TncAction.scss";

interface TncActionsProps {
  isAccepting?: boolean;
  tncLoading?: boolean;
  handleAgree: () => void;
  isPdfLoading?: boolean;
  tncRefetch: () => void;
  refetchPdf: () => void;
  clickToolbarButtonByTitle: (_title: string) => void;
  agreementText?: string;
}

const TncActions = ({
  isAccepting,
  tncLoading,
  handleAgree,
  isPdfLoading,
  tncRefetch,
  refetchPdf,
  clickToolbarButtonByTitle,
  agreementText = "By clicking 'AGREE & CONTINUE', you accept the Terms and Conditions.",
}: TncActionsProps) => {
  return (
    <div className="tnc-actions-container">
      <span className="tnc-agreement-text">{agreementText}</span>
      <div className="tnc-actions-buttons">
        <div className="tnc-actions-left">
          <Button
            svgIcon={tncLoading || isPdfLoading ? rotateIcon : printIcon}
            disabled={tncLoading || isPdfLoading}
            themeColor="primary"
            fillMode="outline"
            onClick={() => {
              tncRefetch();
              refetchPdf();
            }}
            size={"large"}
          >
            Refresh
          </Button>

          <Button
            svgIcon={downloadIcon}
            themeColor="tertiary"
            fillMode="outline"
            onClick={() => clickToolbarButtonByTitle("Download")}
            disabled={tncLoading || isPdfLoading}
            size={"large"}
          >
            Download
          </Button>

          <Button
            svgIcon={printIcon}
            disabled={tncLoading || isPdfLoading}
            themeColor="primary"
            fillMode="outline"
            onClick={() => clickToolbarButtonByTitle("Print")}
            size={"large"}
          >
            Print
          </Button>
        </div>

        <Button
          themeColor="tertiary"
          fillMode="solid"
          onClick={handleAgree}
          disabled={isAccepting || tncLoading || isPdfLoading}
          //svgIcon={isAccepting ? <Spin size="small" /> : undefined}
          size={"large"}
        >
          AGREE & CONTINUE
        </Button>
      </div>
    </div>
  );
};

export default TncActions;
