import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "@/store/store";
import { TncStatus, type EvaluateTncResponse, type TncDocumentState } from "./types/TermsAndConditions";
import { tncApiSlice } from "./api/tncApiSlice";

export type ThemeMode = "light" | "dark";

export interface AppState {
  themeMode: ThemeMode;
  tnc: TncDocumentState;
}

const initialState: AppState = {
  themeMode: "light",
  tnc: {
    isValidated: false,
    document: null,
  }
};

export const appSlice = createSlice({
  name: "app",
  initialState,
  reducers: {
    toggleThemeMode: (state) => {
      const newMode = state.themeMode === "light" ? "dark" : "light";
      state.themeMode = newMode;
    },

    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.themeMode = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(tncApiSlice.endpoints.evaluateTnc.matchFulfilled, handleEvaluateTncSuccess)
      .addMatcher(tncApiSlice.endpoints.acceptTnc.matchFulfilled, handleAcceptTncSuccess);
  },
});

const handleEvaluateTncSuccess = (state: AppState, action: PayloadAction<EvaluateTncResponse>) => {
  const { status, termAndConditionId, statementOfAgreement, triggerType, file } = action.payload;

  if (status === TncStatus.ACCEPTED) {
    state.tnc.isValidated = true;
    state.tnc.document = null;
  } else if (status === TncStatus.PENDING_ACCEPTANCE) {
    state.tnc.document = {
      termAndConditionId: termAndConditionId!,
      statementOfAgreement: statementOfAgreement!,
      triggerType: triggerType!,
      ...file!
    };
  }
};

const handleAcceptTncSuccess = (state: AppState) => {
  state.tnc.isValidated = true;
  state.tnc.document = null;
};

export const { toggleThemeMode, setThemeMode } = appSlice.actions;
export const selectThemeMode = (state: RootState) => state.app.themeMode;

export default appSlice.reducer;
