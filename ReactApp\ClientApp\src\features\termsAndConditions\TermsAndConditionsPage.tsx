import { PDFViewer } from "@progress/kendo-react-pdf-viewer";
import "./TermsAndConditionsPage.scss";
import TncActions from "./components/TncActions";
import { useTncController } from "./hooks/useTncController";

const TermsAndConditionsPage = () => {
  const {
    tncDocument,
    pdfViewerRef,
    tncRefetch,
    tncLoading,
    isAccepting,
    onDocumentLoad,
    handleAgree,
    clickToolbarButtonByTitle,
    pdfBlobUrl,
    isPdfLoading,
    refetchPdf,
  } = useTncController();

  if (!tncDocument || !tncDocument.document) return null;

  return (
    <>
      <div className="pdfViewMainContainer">
        <div className="pdfViewContainer">
          <PDFViewer
            url={pdfBlobUrl}
            onLoad={onDocumentLoad}
            ref={pdfViewerRef}
            tools={["pager", "print", "download", "selection", "zoomInOut"]}
            style={{ height: "100%" }}
            zoom={0.9}
          />
        </div>

        <TncActions
          isAccepting={isAccepting}
          tncLoading={tncLoading}
          handleAgree={handleAgree}
          isPdfLoading={isPdfLoading}
          tncRefetch={tncRefetch}
          refetchPdf={refetchPdf}
          clickToolbarButtonByTitle={clickToolbarButtonByTitle}
          agreementText={tncDocument.document.statementOfAgreement}
        />
      </div>
    </>
  );
};

export default TermsAndConditionsPage;
