import type { RouteObject } from "react-router-dom";
import { ROUTES } from "@/constants/routes";
import DashboardLayout from "@/layouts/DashboardLayout";
import RequireAuth from "@/wrappers/RequireAuth";
import React, { Suspense } from "react";
import LoaderComponent from "@/components/Loader/Loader";
import TermsAndConditionsLayout from "@/layouts/TermsAndConditionsLayout";
import { TermsAndConditionsPage } from "@/features/termsAndConditions";
import { PortalControlsPage } from "@/features/portalControls";

const SubmitedFiles = React.lazy(
  () => import("@/features/submitedFiles/SubmitedFilesPage")
);

const PublishedFiles = React.lazy(
  () => import("@/features/publishedFiles/publishedFilesPage")
);

const ErrorPage = React.lazy(() => import("@/features/error/ErrorPage"));

const SampleFeature = React.lazy(
  () => import("@/features/featureTemplate/SampleFeaturePage")
);

const isLocalhost =
  typeof window !== "undefined" && window.location.hostname === "localhost";

export const dashboardRoutes: RouteObject[] = [
  {
    path: ROUTES.DASHBOARD,
    element: (
      <RequireAuth>
        <DashboardLayout />
      </RequireAuth>
    ),
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<LoaderComponent />}>
            <PublishedFiles />
          </Suspense>
        ),
      },
      {
        path: ROUTES.PORTAL_CONTROLS,
        element: (
          <Suspense fallback={<LoaderComponent />}>
            <PortalControlsPage />
          </Suspense>
        ),
      },
      {
        path: ROUTES.SUBMITTED_FILES,
        element: (
          <Suspense fallback={<LoaderComponent />}>
            <SubmitedFiles />
          </Suspense>
        ),
      },
      {
        path: ROUTES.PUBLISHED_FILES,
        element: (
          <Suspense fallback={<LoaderComponent />}>
            <PublishedFiles />
          </Suspense>
        ),
      },
      {
        path: ROUTES.ERROR,
        element: (
          <Suspense fallback={<LoaderComponent />}>
            <ErrorPage />
          </Suspense>
        ),
      },
      // Conditionally add SampleFeature route only in localhost
      ...(isLocalhost
        ? [
            {
              path: ROUTES.SAMPLE_FEATURE,
              element: (
                <Suspense fallback={<LoaderComponent />}>
                  <SampleFeature />
                </Suspense>
              ),
            },
          ]
        : []),
    ],
  },
  {
    path: ROUTES.TERMS_AND_CONDITIONS,
    element: <TermsAndConditionsLayout />,
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<div>Loading Terms and Conditions...</div>}>
            <TermsAndConditionsPage />
          </Suspense>
        ),
      },
    ],
  },
];
