import React from "react";
import {
  <PERSON><PERSON>,
  GridColumn as <PERSON><PERSON><PERSON>,
  GridColumnMenuFilter,
  GridColumnMenuCheckboxFilter,
  type GridFilterOperators,
  type GridCellProps,
} from "@progress/kendo-react-grid";
import { <PERSON><PERSON> } from "@progress/kendo-react-data-tools";
import type { PagerProps } from "@progress/kendo-react-data-tools";
import { Button, DropDownButton } from "@progress/kendo-react-buttons";
import { InputSuffix, TextBox } from "@progress/kendo-react-inputs";
import { SyncIcon } from "@/components/SyncIcon/SyncIcon";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { usePublishedFilesController } from "./hooks/usePublishedFilesController";
import { useDownloadDialog } from "./hooks/useDownloadDialog";
import { DownloadDialog } from "./components/DownloadDialog";
import "./PublishedFilesPage.scss";

const filterOperators: GridFilterOperators = {
  text: [{ text: "grid.filterContainsOperator", operator: "contains" }],
  numeric: [{ text: "grid.filterEqOperator", operator: "eq" }],
  date: [
    { text: "grid.filterEqOperator", operator: "eq" },
    { text: "grid.filterGteOperator", operator: "gte" },
    { text: "grid.filterLteOperator", operator: "lte" },
  ],
  boolean: [{ text: "grid.filterEqOperator", operator: "eq" }],
};

const PublishedFilesPage: React.FC = () => {
  const {
    currentPageData,
    total,
    skip,
    take,
    sort,
    filter,
    fileTypeOptions,
    yearOptions,
    primaryCategoryOptions,
    secondaryCategoryOptions,
    headerSelectionValue,
    selectedIds,
    onSelectionChange,
    onHeaderSelectionChange,
    onSortChange,
    onPageChange,
    onFilterChange,
  } = usePublishedFilesController();

  const {
    state: dialogState,
    openDialog,
    closeDialog,
    handleDownloadFiles,
    handleCancel,
  } = useDownloadDialog();

  const headerActions = (
    <>
      <TextBox
        placeholder="Search Files"
        className="published-files-searchbox"
        suffix={
          <InputSuffix>
            <span className="k-icon k-i-search" />
          </InputSuffix>
        }
      />
      <Button icon="search">Search</Button>
      <DropDownButton
        icon="download"
        fillMode="outline"
        disabled={selectedIds.length === 0}
        text="Download"
        items={
          selectedIds.length <= 1
            ? [{ text: "Download File(s)", id: "download" }]
            : [
                { text: "Download File(s)", id: "download" },
                { text: "Download as Zip File", id: "download-zip" },
              ]
        }
        onItemClick={(e) => {
          if (e.item.id === "download") {
            if (selectedIds.length > 1) {
              const selectedFiles = currentPageData
                .filter((item) => selectedIds.includes(item.id))
                .map((item) => item.fileName);
              openDialog("files", selectedFiles, selectedIds);
            } else {
              handleDownloadFiles("files", selectedIds);
            }
          } else if (e.item.id === "download-zip") {
            const selectedFiles = currentPageData
              .filter((item) => selectedIds.includes(item.id))
              .map((item) => item.fileName);
            openDialog("zip", selectedFiles, selectedIds);
          }
        }}
      />
    </>
  );

  const FileNameCell = (props: GridCellProps) => {
    if (!props.dataItem) return null;

    return (
      <td className="file-name-cell">
        <span>{props.dataItem.fileName}</span>
      </td>
    );
  };

  return (
    <>
      <SectionLayout headerActions={headerActions}>
        <Grid
          filterable
          style={{ height: "100%" }}
          className="custom-pager"
          data={currentPageData}
          dataItemKey="id"
          skip={skip}
          take={take}
          total={total}
          pageable={{
            buttonCount: 5,
            info: false,
            pageSizes: false,
            previousNext: true,
            type: "numeric",
            responsive: true,
          }}
          pager={(props: PagerProps) => {
            const {
              skip,
              take,
              total,
              buttonCount,
              previousNext,
              type,
              responsive,
              onPageChange,
            } = props;
            const currentPage = skip! / take! + 1;
            const totalPages = Math.ceil(total! / take!);
            const start = skip! + 1;
            const end = Math.min(skip! + take!, total!);

            return (
              <div className="custom-pager-wrap">
                <span className="custom-page-info">
                  Page {currentPage} of {totalPages}
                </span>

                <Pager
                  className="custom-pager"
                  skip={skip}
                  take={take}
                  total={total}
                  buttonCount={buttonCount}
                  previousNext={previousNext}
                  type={type}
                  responsive={responsive}
                  onPageChange={onPageChange}
                  info={false}
                  pageSizes={undefined}
                />

                <span className="custom-default-info">
                  {start} - {end} of {total} items
                </span>

                <Button
                  fillMode="flat"
                  className="custom-refresh-btn"
                  onClick={() => {}}
                  title="Refresh"
                >
                  <SyncIcon />
                </Button>
              </div>
            );
          }}
          onPageChange={onPageChange}
          sortable={{ allowUnsort: true, mode: "multiple" }}
          sort={sort}
          onSortChange={onSortChange}
          filter={filter}
          onFilterChange={onFilterChange}
          filterOperators={filterOperators}
          resizable
          selectable={{
            enabled: true,
            mode: "multiple",
            drag: false,
            cell: false,
          }}
          onSelectionChange={onSelectionChange}
          onHeaderSelectionChange={onHeaderSelectionChange}
        >
          <Column
            columnType="checkbox"
            width="50px"
            headerSelectionValue={headerSelectionValue}
            filterable={false}
            sortable={false}
          />
          <Column
            field="fileName"
            title="File Name"
            filter="text"
            resizable={true}
            cells={{
              data: (props: GridCellProps) => <FileNameCell {...props} />,
            }}
          />

          <Column
            field="fileType"
            title="File Type"
            filterable
            resizable
            columnMenu={(props) => (
              <GridColumnMenuCheckboxFilter
                {...props}
                data={fileTypeOptions}
                filterOperators={filterOperators}
                uniqueData
              />
            )}
          />

          <Column
            field="fileSize"
            title="File Size"
            filterable={false}
            resizable
          />

          <Column
            field="year"
            title="Year"
            filterable
            resizable
            columnMenu={(props) => (
              <GridColumnMenuCheckboxFilter
                {...props}
                data={yearOptions}
                filterOperators={filterOperators}
                uniqueData
              />
            )}
          />

          <Column
            field="publishedDate"
            title="Published Date"
            filter="date"
            columnMenu={(props) => <GridColumnMenuFilter {...props} />}
            format="{0:dd/MM/yyyy}"
            resizable
          />

          <Column
            field="primaryCategory"
            title="Primary Category"
            filterable
            resizable
            columnMenu={(props) => (
              <GridColumnMenuCheckboxFilter
                {...props}
                data={primaryCategoryOptions}
                filterOperators={filterOperators}
                uniqueData
              />
            )}
          />

          <Column
            field="secondaryCategory"
            title="Secondary Category"
            filterable
            resizable
            columnMenu={(props) => (
              <GridColumnMenuCheckboxFilter
                {...props}
                data={secondaryCategoryOptions}
                filterOperators={filterOperators}
                uniqueData
              />
            )}
          />
        </Grid>
      </SectionLayout>

      <DownloadDialog
        state={dialogState}
        onClose={closeDialog}
        onDownload={handleDownloadFiles}
        onCancel={handleCancel}
      />
    </>
  );
};

export default PublishedFilesPage;
