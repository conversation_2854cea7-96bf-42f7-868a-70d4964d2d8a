import { configureStore } from "@reduxjs/toolkit";
import type { ThunkAction, Action } from "@reduxjs/toolkit";
import appReducer from "@/appSlice";
import oktaReducer from "./oktaSlice";
import discoveryReducer from "./discoverySlice";
import { sampleApiSlice } from "@/api/sampleApiSlice";
import { tncApiSlice } from "@/api/tncApiSlice";

// add slices here
export const store = configureStore({
  reducer: {
    app: appReducer,
    okta: oktaReducer,
    discovery: discoveryReducer,
    [sampleApiSlice.reducerPath]: sampleApiSlice.reducer,
    [tncApiSlice.reducerPath]: tncApiSlice.reducer,
    // Add other slices here as needed
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(sampleApiSlice.middleware)
      .concat(tncApiSlice.middleware)
  // add RTK Query middleware
});

// strong typing
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// async thunk actions
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
