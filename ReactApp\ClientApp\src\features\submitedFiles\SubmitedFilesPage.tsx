import React from "react";
import {
  <PERSON><PERSON>,
  GridColumn as <PERSON><PERSON><PERSON>,
  GridColumnMenuFilter,
  GridColumnMenuCheckboxFilter,
  type GridFilterOperators,
} from "@progress/kendo-react-grid";
import { Pager } from "@progress/kendo-react-data-tools";
import type { PagerProps } from "@progress/kendo-react-data-tools";
import { Button } from "@progress/kendo-react-buttons";
import { InputSuffix, TextBox } from "@progress/kendo-react-inputs";
import { SyncIcon } from "@/components/SyncIcon/SyncIcon";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { useSubmitedFilesController } from "./hooks/useSubmitedFilesController";
import "./SubmitedFilesPage.scss";

const filterOperators: GridFilterOperators = {
  text: [{ text: "grid.filterContainsOperator", operator: "contains" }],
  numeric: [{ text: "grid.filterEqOperator", operator: "eq" }],
  date: [
    { text: "grid.filterEqOperator", operator: "eq" },
    { text: "grid.filterGteOperator", operator: "gte" },
    { text: "grid.filterLteOperator", operator: "lte" },
  ],
  boolean: [{ text: "grid.filterEqOperator", operator: "eq" }],
};

const SubmitedFilesPage: React.FC = () => {
  const {
    currentPageData,
    total,
    skip,
    take,
    sort,
    filter,
    fileTypeOptions,
    fileStatusOptions,
    approvedUserOptions,
    rejectedUserOptions,
    headerSelectionValue,
    selectedIds,
    onSelectionChange,
    onHeaderSelectionChange,
    onSortChange,
    onPageChange,
    onFilterChange,
    handleDownload,
  } = useSubmitedFilesController();

  const headerActions = (
    <>
      <TextBox
        placeholder="Search Files"
        className="published-files-searchbox"
        suffix={
          <InputSuffix>
            <span className="k-icon k-i-search" />
          </InputSuffix>
        }
      />
      <Button icon="search">Search</Button>
      <Button
        icon="download"
        fillMode="outline"
        disabled={selectedIds.length === 0}
        onClick={() => handleDownload(selectedIds)}
      >
        Download
      </Button>
    </>
  );

  return (
    <SectionLayout headerActions={headerActions}>
      <Grid
        filterable
        style={{ height: "100%" }}
        className="custom-pager"
        data={currentPageData}
        dataItemKey="id"
        skip={skip}
        take={take}
        total={total}
        pageable={{
          buttonCount: 5,
          info: false,
          pageSizes: false,
          previousNext: true,
          type: "numeric",
          responsive: true,
        }}
        pager={(props: PagerProps) => {
          const {
            skip,
            take,
            total,
            buttonCount,
            previousNext,
            type,
            responsive,
            onPageChange,
          } = props;
          const currentPage = skip! / take! + 1;
          const totalPages = Math.ceil(total! / take!);
          const start = skip! + 1;
          const end = Math.min(skip! + take!, total!);

          return (
            <div className="custom-pager-wrap">
              <span className="custom-page-info">
                Page {currentPage} of {totalPages}
              </span>

              <Pager
                className="custom-pager"
                skip={skip}
                take={take}
                total={total}
                buttonCount={buttonCount}
                previousNext={previousNext}
                type={type}
                responsive={responsive}
                onPageChange={onPageChange}
                info={false}
                pageSizes={undefined}
              />

              <span className="custom-default-info">
                {start} - {end} of {total} items
              </span>

              <Button
                fillMode="flat"
                className="custom-refresh-btn"
                onClick={() => {}}
                title="Refresh"
              >
                <SyncIcon />
              </Button>
            </div>
          );
        }}
        onPageChange={onPageChange}
        sortable={{ allowUnsort: true, mode: "multiple" }}
        sort={sort}
        onSortChange={onSortChange}
        filter={filter}
        onFilterChange={onFilterChange}
        filterOperators={filterOperators}
        resizable
        selectable={{
          enabled: true,
          mode: "multiple",
          drag: false,
          cell: false,
        }}
        onSelectionChange={onSelectionChange}
        onHeaderSelectionChange={onHeaderSelectionChange}
      >
        <Column
          columnType="checkbox"
          width="50px"
          headerSelectionValue={headerSelectionValue}
          filterable={false}
          sortable={false}
        />
        <Column field="fileName" title="File Name" filter="text" resizable />

        <Column
          field="fileType"
          title="File Type"
          filterable
          resizable
          columnMenu={(props) => (
            <GridColumnMenuCheckboxFilter
              {...props}
              data={fileTypeOptions}
              filterOperators={filterOperators}
              uniqueData
            />
          )}
        />

        <Column
          field="fileSize"
          title="File Size"
          filterable={false}
          resizable
        />

        <Column
          field="fileStatus"
          title="File Status"
          filterable
          resizable
          columnMenu={(props) => (
            <GridColumnMenuCheckboxFilter
              {...props}
              data={fileStatusOptions}
              filterOperators={filterOperators}
              uniqueData
            />
          )}
        />

        <Column
          field="uploadedDate"
          title="Uploaded Date"
          filter="date"
          columnMenu={(props) => <GridColumnMenuFilter {...props} />}
          format="{0:dd/MM/yyyy}"
          resizable
        />

        <Column
          field="approvedDate"
          title="Approved Date"
          filter="date"
          columnMenu={(props) => <GridColumnMenuFilter {...props} />}
          format="{0:dd/MM/yyyy}"
          resizable
        />

        <Column
          field="approvedUser"
          title="Approved User"
          filterable
          resizable
          columnMenu={(props) => (
            <GridColumnMenuCheckboxFilter
              {...props}
              data={approvedUserOptions}
              filterOperators={filterOperators}
              uniqueData
            />
          )}
        />

        <Column
          field="rejectedDate"
          title="Rejected Date"
          filter="date"
          columnMenu={(props) => <GridColumnMenuFilter {...props} />}
          format="{0:dd/MM/yyyy}"
          resizable
        />

        <Column
          field="rejectedUser"
          title="Rejected User"
          filterable
          resizable
          columnMenu={(props) => (
            <GridColumnMenuCheckboxFilter
              {...props}
              data={rejectedUserOptions}
              filterOperators={filterOperators}
              uniqueData
            />
          )}
        />
      </Grid>
    </SectionLayout>
  );
};

export default SubmitedFilesPage;
