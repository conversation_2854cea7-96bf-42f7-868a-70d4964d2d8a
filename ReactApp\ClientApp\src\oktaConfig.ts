import config from "@/config/app.config";

const oktaConfig = {
  issuer: `${config.discoveryURL}/oauth2/default`,
  clientId: config.clientId,
  redirectUri: window.location.origin + "/login/callback",
  postLogoutRedirectUri: window.location.origin + "/logout",
  scopes: ["openid", "profile", "tenancy"],
  tokenManager: {
    autoRenew: true,
    secure: true,
    storageKey: "AP_AUTH_TOKEN",
  },
};

export default oktaConfig;
