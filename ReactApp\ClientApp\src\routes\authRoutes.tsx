import type { RouteObject } from "react-router-dom";
import React, { Suspense } from "react";
import { ROUTES } from "@/constants/routes";
import UnauthorizedLayout from "@/layouts/UnauthorizedLayout";
import { LoginCallback } from "@okta/okta-react";
import LoaderComponent from "@/components/Loader/Loader";

const LoginPage = React.lazy(() => import("@/features/auth/LoginPage"));

export const authRoutes: RouteObject[] = [
  {
    element: <UnauthorizedLayout />,
    children: [
      {
        path: ROUTES.LOGIN,
        element: (
          <Suspense fallback={<LoaderComponent />}>
            <LoginPage />
          </Suspense>
        ),
      },
      {
        path: ROUTES.LOGIN_CALLBACK,
        element: <LoginCallback />,
      },
    ],
  },
];
