import { fetchBaseQuery } from "@reduxjs/toolkit/query";
import type {
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import type { RootState } from "@/store/store";
import config from "@/config/app.config";
import DISCOVERY_STATUS from "@/constants/discoveryStatus";

// Extend FetchArgs to include optional `meta`
type CustomFetchArgs = FetchArgs & { meta?: number };

export const rawBaseQuery = fetchBaseQuery({
  baseUrl: config.baseUrl, // fallback if discovery fails
  prepareHeaders: (headers, { getState }) => {
    const state = getState() as RootState;
    const accessToken = state.okta?.idToken;

    if (accessToken) {
      headers.set("Authorization", "Bearer " + accessToken);
    }

    return headers;
  },
});

export const baseQueryWithReauth: BaseQueryFn<
  string | CustomFetchArgs, // use extended type here
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;
  const tenantCode = state.okta.userInfo?.tenantCode?.[0];
  const discoveryEndpoints = state.discovery?.baseUrls ?? [];

  if (
    state.discovery.status !== DISCOVERY_STATUS.SUCCESS ||
    !tenantCode ||
    discoveryEndpoints.length === 0
  ) {
    return {
      error: {
        status: "FETCH_ERROR",
        error: "Discovery base URLs or tenant not ready yet",
      },
    };
  }

  // Get serviceKey from meta
  const serviceKey =
    typeof args === "object" && "meta" in args ? args.meta : undefined;

  // Find matching baseUrl from discovery
  const matchingEntry = discoveryEndpoints.find(
    (entry) => entry.serviceType === serviceKey,
  );

  const baseUrl = matchingEntry?.entrypointAddress || config.baseUrl;

  const prepareUrl = (relativeUrl: string) => {
    const normalized = relativeUrl.replace(/^\/+/, "");
    return `${baseUrl}/${tenantCode}/${normalized}`;
  };

  // Adjust args.url
  if (typeof args === "string") {
    args = prepareUrl(args);
  } else if (args?.url) {
    args.url = prepareUrl(args.url);
  }

  let result = await rawBaseQuery(args, api, extraOptions);

  // Optional: Retry after token refresh if 401
  if (result.error && result.error.status === 401) {
    const refreshResult = await rawBaseQuery(
      "/refreshToken",
      api,
      extraOptions,
    );
    if (refreshResult.data) {
      result = await rawBaseQuery(args, api, extraOptions);
    }
  }

  return result;
};
