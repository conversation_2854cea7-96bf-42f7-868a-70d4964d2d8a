import { useState, useEffect, useCallback } from "react";
import logger from "@/utils/logger";
import { format } from 'date-fns';

export interface DownloadDialogState {
  isOpen: boolean;
  mode: "files" | "zip" | null;
  selectedFiles: string[];
  selectedIds: number[];
  countdown: number;
  isCountdownActive: boolean;
}

export const useDownloadDialog = () => {
  const [state, setState] = useState<DownloadDialogState>({
    isOpen: false,
    mode: null,
    selectedFiles: [],
    selectedIds: [],
    countdown: 10,
    isCountdownActive: false,
  });

  const generateZipFileName = useCallback(() => {
    const timestamp = format(new Date(), "yyyy-MM-dd_HH-mm-ss");
    return `PublishedFiles_${timestamp}.zip`;
  }, []);

  const handleDownloadFiles = useCallback((mode?: "files" | "zip", selectedIds?: number[]) => {
    const currentMode = mode || state.mode;
    const currentSelectedIds = selectedIds || state.selectedIds;
    
    if (currentMode === "files") {
      logger.info(`The following Files are being downloaded - ${currentSelectedIds}`);
    } else if (currentMode === "zip") {
      const zipFileName = generateZipFileName();
      logger.info(`The following Files are being downloaded - ${zipFileName}`);
    }
  }, [state.mode, state.selectedIds, generateZipFileName]);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    
    if (state.isCountdownActive && state.countdown > 0) {
      interval = setInterval(() => {
        setState(prev => ({
          ...prev,
          countdown: prev.countdown - 1,
        }));
      }, 1000);
    } else if (state.countdown === 0 && state.isCountdownActive) {
      setState(prev => ({
        ...prev,
        isCountdownActive: false,
      }));
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [state.isCountdownActive, state.countdown]);

  // Trigger download when dialog opens
  useEffect(() => {
    if (state.isOpen && state.mode && state.selectedIds.length > 0) {
      handleDownloadFiles(state.mode, state.selectedIds);
    }
  }, [state.isOpen, state.mode, state.selectedIds, handleDownloadFiles]);

  const openDialog = useCallback((
    mode: "files" | "zip",
    selectedFiles: string[],
    selectedIds: number[]
  ) => {
    setState({
      isOpen: true,
      mode,
      selectedFiles,
      selectedIds,
      countdown: 10,
      isCountdownActive: true,
    });
  }, []);

  const closeDialog = useCallback(() => {
    setState({
      isOpen: false,
      mode: null,
      selectedFiles: [],
      selectedIds: [],
      countdown: 10,
      isCountdownActive: false,
    });
  }, []);

  const handleCancel = useCallback(() => {
    closeDialog();
  }, [closeDialog]);

  return {
    state,
    openDialog,
    closeDialog,
    handleDownloadFiles,
    handleCancel,
  };
};
