import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQueryWithReauth } from './interceptorsSlice';
import type { PortalMenuResponse } from '@/types/Dashboard';
import httpVerbs from '@/utils/http/httpVerbs';
import { OperationalServiceTypes } from "@iris/discovery.fe.client";

export const dashboardApiSlice = createApi({
    reducerPath: '/dashboard',
    baseQuery: baseQueryWithReauth,
    endpoints: (builder) => ({
        getPortalMenu: builder.query<PortalMenuResponse, void>({
            query: () => ({
                url: 'api/users/portal-menu',
                headers: {
                    'Content-Type': 'application/json',
                },
                method: httpVerbs.GET,
                meta: OperationalServiceTypes.UserService,
            }),
        }),
    }),
});

export const { useGetPortalMenuQuery } = dashboardApiSlice;
